import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SimpleTokenManagerService } from '../../../../shared/services/simple-token-manager.service';
import { TokenProvider } from '../../../../domain/common/entities/oauth-token.entity';
import { BaseApiService } from '../../base-api.service';

@Injectable()
export class ShopifyAuthService {
  private readonly logger = new Logger(ShopifyAuthService.name);
  private readonly shopDomain: string;
  private readonly accessToken: string;
  private readonly apiVersion: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly tokenManager: SimpleTokenManagerService,
    private readonly apiService: BaseApiService,
  ) {
    this.shopDomain = configService.get<string>('SHOPIFY_SHOP_DOMAIN', '');
    this.accessToken = configService.get<string>('SHOPIFY_ACCESS_TOKEN', '');
    this.apiVersion = configService.get<string>('SHOPIFY_API_VERSION', '2023-10');
  }

  /**
   * Initialize authentication
   */
  async initialize(): Promise<void> {
    // Register the refresh function with the token manager (Shopify doesn't use refresh tokens)
    this.tokenManager.registerRefreshFunction(TokenProvider.SHOPIFY, async () => {
      throw new Error('Shopify tokens do not support automatic refresh. Please update manually.');
    });

    // Migrate existing tokens from environment to secure storage
    await this.migrateExistingTokens();

    // Set up authentication with token manager
    await this.setupAuthentication();
  }

  /**
   * Migrate existing tokens from environment to secure storage
   */
  private async migrateExistingTokens(): Promise<void> {
    if (this.accessToken && this.shopDomain) {
      try {
        const tokenStatus = await this.tokenManager.getTokenStatus(
          TokenProvider.SHOPIFY,
          this.shopDomain,
        );
        if (!tokenStatus.hasToken) {
          this.logger.log('Migrating existing Shopify tokens to secure storage...');

          await this.tokenManager.storeTokens(TokenProvider.SHOPIFY, {
            accessToken: this.accessToken,
            refreshToken: '', // Shopify doesn't use refresh tokens
            shopId: this.shopDomain,
            shopName: this.shopDomain,
            scopes: 'read_products,read_orders,write_products,write_orders',
            metadata: {
              migratedFromEnv: true,
              migratedAt: new Date().toISOString(),
              apiVersion: this.apiVersion,
            },
          });

          this.logger.log('Successfully migrated Shopify tokens to secure storage');
        }
      } catch (error) {
        this.logger.error('Failed to migrate existing tokens:', error);
      }
    }
  }

  /**
   * Set up authentication using token manager
   */
  private async setupAuthentication(): Promise<void> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.SHOPIFY,
        this.shopDomain,
      );
      if (accessToken) {
        this.setAccessToken(accessToken);
        this.logger.log('Successfully set up Shopify authentication from token manager');
      } else {
        this.logger.warn('No valid Shopify access token available');
      }
    } catch (error) {
      this.logger.error('Failed to set up authentication:', error);
    }
  }

  /**
   * Ensure we have a valid access token before making API calls
   */
  async ensureValidToken(): Promise<boolean> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.SHOPIFY,
        this.shopDomain,
      );
      if (accessToken) {
        this.setAccessToken(accessToken);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to ensure valid token:', error);
      return false;
    }
  }

  /**
   * Health check for Shopify API
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Ensure we have a valid token before making the health check
      const hasValidToken = await this.ensureValidToken();
      if (!hasValidToken) {
        this.logger.warn('No valid Shopify access token available for health check');
        return false;
      }

      await this.apiService.get('/shop.json');
      return true;
    } catch (error) {
      this.logger.error('Shopify API health check failed:', error);
      return false;
    }
  }

  /**
   * Get shop information
   */
  async getShop(): Promise<any> {
    const response = await this.apiService.get('/shop.json');
    return response.data.shop;
  }

  /**
   * Set access token
   */
  setAccessToken(token: string): void {
    this.apiService.setHeader('X-Shopify-Access-Token', token);
  }

  /**
   * Remove access token
   */
  removeAccessToken(): void {
    this.apiService.removeHeader('X-Shopify-Access-Token');
  }

  /**
   * Store new OAuth tokens (for manual token updates)
   */
  async storeTokens(tokenData: {
    accessToken: string;
    expiresAt?: Date;
    scopes?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.tokenManager.storeTokens(TokenProvider.SHOPIFY, {
      ...tokenData,
      refreshToken: '', // Shopify doesn't use refresh tokens
      shopId: this.shopDomain,
      shopName: this.shopDomain,
    });

    // Update current authentication
    await this.setupAuthentication();
  }

  /**
   * Get token status
   */
  async getTokenStatus(): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    return this.tokenManager.getTokenStatus(TokenProvider.SHOPIFY, this.shopDomain);
  }

  /**
   * Revoke stored tokens
   */
  async revokeTokens(): Promise<void> {
    await this.tokenManager.revokeTokens(TokenProvider.SHOPIFY, this.shopDomain);
    this.removeAccessToken();
  }
}
