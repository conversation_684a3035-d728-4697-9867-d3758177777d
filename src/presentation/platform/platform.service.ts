import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { AmazonSyncService } from './services/amazon-sync.service';
import { EtsySyncService } from './services/etsy-sync.service';
import { ShopifySyncService } from './services/shopify-sync.service';
import { TokenManagerService } from '../../shared/services/token-manager.service';
import { TokenProvider } from '../../domain/common/entities/oauth-token.entity';
import { EtsyOrderSyncQueueOptions } from './services/etsy-order-sync-queue.service';
// import { PlatformSyncService } from './services/platform-sync.service'; // temporarily disabled

export interface SyncResult {
  platform: PlatformSource;
  success: boolean;
  productsProcessed: number;
  ordersProcessed: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

export interface PlatformStatus {
  platform: PlatformSource;
  isHealthy: boolean;
  lastSync?: Date;
  lastError?: string;
  apiStatus: 'connected' | 'disconnected' | 'error';
}

@Injectable()
export class PlatformService {
  private readonly logger = new Logger(PlatformService.name);

  constructor(
    private readonly amazonSyncService: AmazonSyncService,
    private readonly etsySyncService: EtsySyncService,
    private readonly shopifySyncService: ShopifySyncService,
    private readonly tokenManager: TokenManagerService,
    private readonly configService: ConfigService,
    // private readonly platformSyncService: PlatformSyncService, // temporarily disabled
  ) {}

  /**
   * Sync all platforms
   */
  async syncAllPlatforms(): Promise<SyncResult[]> {
    this.logger.log('Starting sync for all platforms');
    const startTime = Date.now();

    const results: SyncResult[] = [];

    // Sync each platform in parallel
    const syncPromises = [
      this.syncPlatform(PlatformSource.AMAZON),
      this.syncPlatform(PlatformSource.ETSY),
      this.syncPlatform(PlatformSource.SHOPIFY),
    ];

    const platformResults = await Promise.allSettled(syncPromises);

    platformResults.forEach((result, index) => {
      const platforms = [PlatformSource.AMAZON, PlatformSource.ETSY, PlatformSource.SHOPIFY];
      const platform = platforms[index];

      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          platform,
          success: false,
          productsProcessed: 0,
          ordersProcessed: 0,
          errors: [result.reason?.message || 'Unknown error'],
          duration: 0,
          timestamp: new Date(),
        });
      }
    });

    const totalDuration = Date.now() - startTime;
    this.logger.log(`Completed sync for all platforms in ${totalDuration}ms`);

    return results;
  }

  /**
   * Sync specific platform
   */
  async syncPlatform(platform: PlatformSource): Promise<SyncResult> {
    this.logger.log(`Starting sync for platform: ${platform}`);
    const startTime = Date.now();

    try {
      let result: SyncResult;

      switch (platform) {
        case PlatformSource.AMAZON:
          result = await this.amazonSyncService.syncAll();
          break;
        case PlatformSource.ETSY:
          result = await this.etsySyncService.syncAll();
          break;
        case PlatformSource.SHOPIFY:
          result = await this.shopifySyncService.syncAll();
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }

      result.duration = Date.now() - startTime;
      result.timestamp = new Date();

      this.logger.log(
        `Completed sync for ${platform}: ${result.productsProcessed} products, ${result.ordersProcessed} orders in ${result.duration}ms`,
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to sync platform ${platform}:`, error);

      return {
        platform,
        success: false,
        productsProcessed: 0,
        ordersProcessed: 0,
        errors: [error.message || 'Unknown error'],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Sync products only for specific platform
   */
  async syncPlatformProducts(platform: PlatformSource): Promise<SyncResult> {
    this.logger.log(`Starting product sync for platform: ${platform}`);
    const startTime = Date.now();

    try {
      let productsProcessed = 0;

      switch (platform) {
        case PlatformSource.AMAZON:
          productsProcessed = await this.amazonSyncService.syncProducts();
          break;
        case PlatformSource.ETSY:
          productsProcessed = await this.etsySyncService.syncProducts();
          break;
        case PlatformSource.SHOPIFY:
          productsProcessed = await this.shopifySyncService.syncProducts();
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }

      const duration = Date.now() - startTime;

      return {
        platform,
        success: true,
        productsProcessed,
        ordersProcessed: 0,
        errors: [],
        duration,
        timestamp: new Date(),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to sync products for platform ${platform}:`, error);

      return {
        platform,
        success: false,
        productsProcessed: 0,
        ordersProcessed: 0,
        errors: [error.message || 'Unknown error'],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Sync orders only for specific platform
   */
  async syncPlatformOrders(platform: PlatformSource): Promise<SyncResult> {
    this.logger.log(`Starting order sync for platform: ${platform}`);
    const startTime = Date.now();

    try {
      let ordersProcessed = 0;

      switch (platform) {
        case PlatformSource.AMAZON:
          ordersProcessed = await this.amazonSyncService.syncOrders();
          break;
        case PlatformSource.ETSY:
          ordersProcessed = await this.etsySyncService.syncOrders();
          break;
        case PlatformSource.SHOPIFY:
          ordersProcessed = await this.shopifySyncService.syncOrders();
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }

      const duration = Date.now() - startTime;

      return {
        platform,
        success: true,
        productsProcessed: 0,
        ordersProcessed,
        errors: [],
        duration,
        timestamp: new Date(),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to sync orders for platform ${platform}:`, error);

      return {
        platform,
        success: false,
        productsProcessed: 0,
        ordersProcessed: 0,
        errors: [error.message || 'Unknown error'],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Get platform status
   */
  async getPlatformStatus(platform: PlatformSource): Promise<PlatformStatus> {
    try {
      let isHealthy = false;

      switch (platform) {
        case PlatformSource.AMAZON:
          isHealthy = await this.amazonSyncService.healthCheck();
          break;
        case PlatformSource.ETSY:
          isHealthy = await this.etsySyncService.healthCheck();
          break;
        case PlatformSource.SHOPIFY:
          isHealthy = await this.shopifySyncService.healthCheck();
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }

      return {
        platform,
        isHealthy,
        apiStatus: isHealthy ? 'connected' : 'disconnected',
      };
    } catch (error) {
      this.logger.error(`Failed to get status for platform ${platform}:`, error);

      return {
        platform,
        isHealthy: false,
        lastError: error.message || 'Unknown error',
        apiStatus: 'error',
      };
    }
  }

  /**
   * Get status for all platforms
   */
  async getAllPlatformStatuses(): Promise<PlatformStatus[]> {
    const platforms = [PlatformSource.AMAZON, PlatformSource.ETSY, PlatformSource.SHOPIFY];

    const statusPromises = platforms.map(platform => this.getPlatformStatus(platform));
    const statuses = await Promise.allSettled(statusPromises);

    return statuses.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          platform: platforms[index],
          isHealthy: false,
          lastError: result.reason?.message || 'Unknown error',
          apiStatus: 'error' as const,
        };
      }
    });
  }

  /**
   * Update inventory across platforms
   */
  async updateInventoryAcrossPlatforms(
    sku: string,
    quantity: number,
  ): Promise<{
    success: boolean;
    results: Array<{
      platform: PlatformSource;
      success: boolean;
      error?: string;
    }>;
  }> {
    this.logger.log(
      `Updating inventory for SKU ${sku} to quantity ${quantity} across all platforms`,
    );

    const results = await Promise.allSettled([
      this.amazonSyncService.updateInventory(sku, quantity),
      this.etsySyncService.updateInventory(sku, quantity),
      this.shopifySyncService.updateInventory(sku, quantity),
    ]);

    const platforms = [PlatformSource.AMAZON, PlatformSource.ETSY, PlatformSource.SHOPIFY];
    const updateResults = results.map((result, index) => ({
      platform: platforms[index],
      success: result.status === 'fulfilled' && result.value,
      error: result.status === 'rejected' ? result.reason?.message : undefined,
    }));

    const overallSuccess = updateResults.every(result => result.success);

    return {
      success: overallSuccess,
      results: updateResults,
    };
  }

  /**
   * Get sync statistics
   */
  async getSyncStatistics(): Promise<{
    totalProducts: number;
    totalOrders: number;
    lastSyncTimes: Record<PlatformSource, Date | null>;
    syncCounts: Record<PlatformSource, number>;
  }> {
    // return this.platformSyncService.getSyncStatistics(); // temporarily disabled
    throw new Error('Sync statistics temporarily disabled');
  }

  /**
   * Get sync history
   */
  async getSyncHistory(
    platform?: PlatformSource,
    limit: number = 50,
  ): Promise<
    Array<{
      platform: PlatformSource;
      timestamp: Date;
      success: boolean;
      productsProcessed: number;
      ordersProcessed: number;
      duration: number;
      errors: string[];
    }>
  > {
    // return this.platformSyncService.getSyncHistory(platform, limit); // temporarily disabled
    throw new Error('Sync history temporarily disabled');
  }

  /**
   * Sync orders from specific platform with date filters
   */
  async syncOrdersWithFilters(params: {
    platform: PlatformSource;
    startDate?: string;
    endDate?: string;
    limit: number;
    forceUpdate: boolean;
  }): Promise<{
    platform: PlatformSource;
    ordersProcessed: number;
    ordersCreated: number;
    ordersUpdated: number;
    ordersSkipped: number;
    duration: number;
    dateRange: {
      startDate?: string;
      endDate?: string;
    };
    errors: string[];
  }> {
    this.logger.log(`Starting filtered order sync for ${params.platform}`);
    const startTime = Date.now();
    const errors: string[] = [];

    try {
      let result: {
        ordersProcessed: number;
        ordersCreated: number;
        ordersUpdated: number;
        ordersSkipped: number;
      };

      // Call the appropriate sync service based on platform
      switch (params.platform) {
        case PlatformSource.SHOPIFY:
          result = await this.shopifySyncService.syncOrdersWithFilters({
            startDate: params.startDate,
            endDate: params.endDate,
            limit: params.limit,
            forceUpdate: params.forceUpdate,
          });
          break;

        case PlatformSource.ETSY:
          result = await this.etsySyncService.syncOrdersWithFilters({
            startDate: params.startDate,
            endDate: params.endDate,
            limit: params.limit,
            forceUpdate: params.forceUpdate,
          });
          break;

        case PlatformSource.AMAZON:
          result = await this.amazonSyncService.syncOrdersWithFilters({
            startDate: params.startDate,
            endDate: params.endDate,
            limit: params.limit,
            forceUpdate: params.forceUpdate,
          });
          break;

        default:
          throw new Error(`Unsupported platform: ${params.platform}`);
      }

      const duration = Date.now() - startTime;

      // Record sync result
      // await this.platformSyncService.recordSyncResult({ // temporarily disabled
      //   platform: params.platform,
      //   success: true,
      //   productsProcessed: 0, // Only syncing orders
      //   ordersProcessed: result.ordersProcessed,
      //   duration,
      //   errors,
      // });

      this.logger.log(
        `Completed filtered order sync for ${params.platform}: ${result.ordersProcessed} processed, ${result.ordersCreated} created, ${result.ordersUpdated} updated in ${duration}ms`,
      );

      return {
        platform: params.platform,
        ordersProcessed: result.ordersProcessed,
        ordersCreated: result.ordersCreated,
        ordersUpdated: result.ordersUpdated,
        ordersSkipped: result.ordersSkipped,
        duration,
        dateRange: {
          startDate: params.startDate,
          endDate: params.endDate,
        },
        errors,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);

      this.logger.error(`Failed to sync orders for ${params.platform}:`, error);

      // Record failed sync result
      // await this.platformSyncService.recordSyncResult({ // temporarily disabled
      //   platform: params.platform,
      //   success: false,
      //   productsProcessed: 0,
      //   ordersProcessed: 0,
      //   duration,
      //   errors,
      // });

      return {
        platform: params.platform,
        ordersProcessed: 0,
        ordersCreated: 0,
        ordersUpdated: 0,
        ordersSkipped: 0,
        duration,
        dateRange: {
          startDate: params.startDate,
          endDate: params.endDate,
        },
        errors,
      };
    }
  }

  /**
   * Sync orders from all platforms with date filters
   */
  async syncAllOrdersWithFilters(params: {
    startDate?: string;
    endDate?: string;
    limit: number;
    forceUpdate: boolean;
  }): Promise<{
    totalOrdersProcessed: number;
    totalOrdersCreated: number;
    totalOrdersUpdated: number;
    totalDuration: number;
    platforms: Record<string, any>;
  }> {
    this.logger.log('Starting filtered order sync for all platforms');
    const startTime = Date.now();

    const platforms = [PlatformSource.SHOPIFY, PlatformSource.ETSY, PlatformSource.AMAZON];
    const results: Record<string, any> = {};
    let totalOrdersProcessed = 0;
    let totalOrdersCreated = 0;
    let totalOrdersUpdated = 0;

    // Sync each platform sequentially to avoid overwhelming APIs
    for (const platform of platforms) {
      try {
        const result = await this.syncOrdersWithFilters({
          platform,
          startDate: params.startDate,
          endDate: params.endDate,
          limit: params.limit,
          forceUpdate: params.forceUpdate,
        });

        results[platform] = result;
        totalOrdersProcessed += result.ordersProcessed;
        totalOrdersCreated += result.ordersCreated;
        totalOrdersUpdated += result.ordersUpdated;
      } catch (error) {
        this.logger.error(`Failed to sync ${platform}:`, error);
        results[platform] = {
          platform,
          ordersProcessed: 0,
          ordersCreated: 0,
          ordersUpdated: 0,
          ordersSkipped: 0,
          duration: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error'],
        };
      }
    }

    const totalDuration = Date.now() - startTime;

    this.logger.log(
      `Completed filtered order sync for all platforms: ${totalOrdersProcessed} total processed, ${totalOrdersCreated} created, ${totalOrdersUpdated} updated in ${totalDuration}ms`,
    );

    return {
      totalOrdersProcessed,
      totalOrdersCreated,
      totalOrdersUpdated,
      totalDuration,
      platforms: results,
    };
  }

  /**
   * Generate new Etsy tokens and store them securely
   */
  async generateAndStoreEtsyTokens(): Promise<{
    success: boolean;
    accessToken: string;
    refreshToken: string;
  }> {
    try {
      this.logger.log('Generating new Etsy tokens...');

      // Use the Etsy sync service to generate new tokens
      const { accessToken, refreshToken } = await this.etsySyncService.generateEtsyTokens();

      this.logger.log('Storing new Etsy tokens securely...');

      // Store the tokens securely using the token manager
      await this.tokenManager.storeTokens(TokenProvider.ETSY, {
        accessToken,
        refreshToken,
        shopId: '6507168', // Your Etsy shop ID
        scopes: 'shops_r transactions_r',
        metadata: {
          generatedAt: new Date().toISOString(),
          source: 'generateEtsyToken_method',
        },
      });

      this.logger.log('Etsy tokens generated and stored successfully');

      return {
        success: true,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      this.logger.error('Failed to generate and store Etsy tokens:', error);
      throw error;
    }
  }

  /**
   * Generate new Shopify tokens and store them securely
   */
  async generateAndStoreShopifyTokens(): Promise<{
    success: boolean;
    accessToken: string;
    refreshToken: string;
    shopId: string;
  }> {
    try {
      this.logger.log('Generating new Shopify tokens...');

      // Get Shopify credentials from environment
      const shopDomain = this.configService.get<string>('SHOPIFY_SHOP_DOMAIN', '');
      const accessToken = this.configService.get<string>('SHOPIFY_ACCESS_TOKEN', '');

      if (!shopDomain || !accessToken) {
        throw new Error('Shopify credentials not found in environment variables');
      }

      // Extract shop ID from domain (e.g., "knotheoryrings.myshopify.com" -> "knotheoryrings")
      const shopId = shopDomain.replace('.myshopify.com', '');

      this.logger.log('Storing new Shopify tokens securely...');

      // Store the tokens securely using the token manager
      await this.tokenManager.storeTokens(TokenProvider.SHOPIFY, {
        accessToken,
        refreshToken: accessToken, // Shopify uses the same token for both
        shopId,
        shopName: shopDomain,
        scopes: 'read_orders,read_products',
        metadata: {
          generatedAt: new Date().toISOString(),
          source: 'environment_variables',
          shopDomain,
        },
      });

      this.logger.log('Shopify tokens generated and stored successfully');

      return {
        success: true,
        accessToken,
        refreshToken: accessToken,
        shopId,
      };
    } catch (error) {
      this.logger.error('Failed to generate and store Shopify tokens:', error);
      throw error;
    }
  }

  /**
   * Start queue-based order sync for a platform
   */
  async startQueueBasedOrderSync(
    platform: PlatformSource,
    options: EtsyOrderSyncQueueOptions,
  ): Promise<string> {
    this.logger.log(`Starting queue-based order sync for ${platform}`);

    switch (platform) {
      case PlatformSource.ETSY:
        return this.etsySyncService.startQueueBasedOrderSync(options);
      case PlatformSource.SHOPIFY:
        // TODO: Implement Shopify queue-based sync
        throw new Error('Queue-based sync not yet implemented for Shopify');
      case PlatformSource.AMAZON:
        // TODO: Implement Amazon queue-based sync
        throw new Error('Queue-based sync not yet implemented for Amazon');
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Get queue sync status
   */
  async getQueueSyncStatus(platform: PlatformSource, jobId: string) {
    switch (platform) {
      case PlatformSource.ETSY:
        return this.etsySyncService.getQueueSyncStatus(jobId);
      case PlatformSource.SHOPIFY:
        // TODO: Implement Shopify queue status
        throw new Error('Queue-based sync not yet implemented for Shopify');
      case PlatformSource.AMAZON:
        // TODO: Implement Amazon queue status
        throw new Error('Queue-based sync not yet implemented for Amazon');
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Cancel queue sync
   */
  async cancelQueueSync(platform: PlatformSource, jobId: string): Promise<boolean> {
    switch (platform) {
      case PlatformSource.ETSY:
        return this.etsySyncService.cancelQueueSync(jobId);
      case PlatformSource.SHOPIFY:
        // TODO: Implement Shopify queue cancel
        throw new Error('Queue-based sync not yet implemented for Shopify');
      case PlatformSource.AMAZON:
        // TODO: Implement Amazon queue cancel
        throw new Error('Queue-based sync not yet implemented for Amazon');
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Get active queue syncs
   */
  async getActiveQueueSyncs(platform: PlatformSource) {
    switch (platform) {
      case PlatformSource.ETSY:
        return this.etsySyncService.getActiveQueueSyncs();
      case PlatformSource.SHOPIFY:
        // TODO: Implement Shopify active syncs
        throw new Error('Queue-based sync not yet implemented for Shopify');
      case PlatformSource.AMAZON:
        // TODO: Implement Amazon active syncs
        throw new Error('Queue-based sync not yet implemented for Amazon');
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Determine if queue sync should be used based on parameters
   */
  shouldUseQueueSync(
    platform: PlatformSource,
    limit: number,
    dateRange?: { startDate?: string; endDate?: string },
  ): boolean {
    switch (platform) {
      case PlatformSource.ETSY:
        return this.etsySyncService.shouldUseQueueSync(limit, dateRange);
      case PlatformSource.SHOPIFY:
        // For now, use same logic as Etsy
        return limit > 100 || this.isLargeDateRange(dateRange);
      case PlatformSource.AMAZON:
        // For now, use same logic as Etsy
        return limit > 100 || this.isLargeDateRange(dateRange);
      default:
        return false;
    }
  }

  /**
   * Helper method to determine if date range is large
   */
  private isLargeDateRange(dateRange?: { startDate?: string; endDate?: string }): boolean {
    if (!dateRange?.startDate || !dateRange?.endDate) {
      return true; // No date range means potentially large dataset
    }

    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    const daysDiff = Math.abs(end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);

    return daysDiff > 30;
  }
}
