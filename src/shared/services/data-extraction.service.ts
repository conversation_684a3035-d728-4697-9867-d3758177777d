import { Injectable, Logger } from '@nestjs/common';
import { SkuExtractionService, SkuParseResult, OrderData } from './sku-extraction.service';
import { PlatformAttributeExtractionService } from './platform-attribute-extraction.service';
import { PlatformSource } from '@domain/product/platform-source.enum';

export interface ExtractedProductData {
  style?: string;
  color?: string;
  size?: string;
  insideDesign?: string;
  outsideDesign?: string;
  design?: string;
  inlayOption?: {
    insideInlay: boolean;
    outsideInlay: boolean;
    color?: string;
  };
  inlayType?: 'gold' | 'silver' | 'clear' | 'none';
  isCustom?: boolean;
  isEngrave?: boolean;
  customization?: string;
  engraving?: string;
  extractionSource: 'sku' | 'platform' | 'mixed';
  confidence: number; // 0-1 score indicating extraction confidence
}

export interface ExtractionInput {
  sku: string;
  title?: string;
  platformData?: any;
  platform: PlatformSource;
  existingData?: Partial<ExtractedProductData>;
}

@Injectable()
export class DataExtractionService {
  private readonly logger = new Logger(DataExtractionService.name);

  constructor(
    private readonly skuExtractionService: SkuExtractionService,
    private readonly platformAttributeExtractionService: PlatformAttributeExtractionService,
  ) {}

  /**
   * Main extraction method that combines SKU and platform-specific extraction
   * SKU extraction takes priority, platform extraction fills gaps
   */
  async extractProductData(input: ExtractionInput): Promise<ExtractedProductData> {
    this.logger.log(`Extracting product data for SKU: ${input.sku}`);

    try {
      // Step 1: Extract from SKU first (highest priority)
      const skuExtracted = this.extractFromSku(input);
      
      // Step 2: Use platform-specific extraction to fill missing attributes
      const platformExtracted = this.extractFromPlatform(input, skuExtracted);
      
      // Step 3: Merge results with SKU taking priority
      const finalResult = this.mergeExtractionResults(skuExtracted, platformExtracted, input.existingData);
      
      // Step 4: Calculate confidence score
      finalResult.confidence = this.calculateConfidence(finalResult, input);
      
      this.logger.log(`Extraction completed with confidence: ${finalResult.confidence}`);
      return finalResult;
      
    } catch (error) {
      this.logger.error(`Failed to extract product data for SKU ${input.sku}:`, error);
      
      // Return minimal data with low confidence
      return {
        extractionSource: 'mixed',
        confidence: 0.1,
        ...input.existingData,
      };
    }
  }

  /**
   * Extract attributes from SKU using the SKU extraction service
   */
  private extractFromSku(input: ExtractionInput): Partial<ExtractedProductData> {
    const orderData: OrderData = {
      sku: input.sku,
      title: input.title,
      source: this.mapPlatformToSource(input.platform),
      ...input.existingData,
    };

    const skuResult = this.skuExtractionService.extractCompleteOrderData(orderData);
    
    return {
      style: skuResult.style,
      color: skuResult.color,
      size: skuResult.size,
      insideDesign: skuResult.insideDesign,
      outsideDesign: skuResult.outsideDesign,
      design: skuResult.design,
      inlayOption: skuResult.inlayOption,
      inlayType: skuResult.inlayType,
    };
  }

  /**
   * Extract attributes from platform-specific data
   */
  private extractFromPlatform(
    input: ExtractionInput, 
    skuExtracted: Partial<ExtractedProductData>
  ): Partial<ExtractedProductData> {
    if (!input.platformData) {
      return {};
    }

    // Convert to platform extraction format
    const platformSkuExtracted = {
      style: skuExtracted.style,
      color: skuExtracted.color,
      size: this.convertSizeToNumber(skuExtracted.size),
      insideDesign: skuExtracted.insideDesign,
      outsideDesign: skuExtracted.outsideDesign,
      design: skuExtracted.design,
      inlayOption: skuExtracted.inlayOption,
      inlayType: skuExtracted.inlayType,
      isCustom: false,
      isEngrave: false,
    };

    const platformResult = this.platformAttributeExtractionService.extractPlatformAttributes(
      input.platform,
      platformSkuExtracted,
      input.platformData,
      input.title,
      input.sku
    );

    return {
      style: platformResult.style,
      color: platformResult.color,
      size: this.convertNumberToSize(platformResult.size),
      insideDesign: platformResult.insideDesign,
      outsideDesign: platformResult.outsideDesign,
      design: platformResult.design,
      inlayOption: platformResult.inlayOption,
      inlayType: platformResult.inlayType,
      isCustom: platformResult.isCustom,
      isEngrave: platformResult.isEngrave,
      customization: platformResult.customization,
      engraving: platformResult.engraving,
    };
  }

  /**
   * Merge extraction results with SKU taking priority over platform data
   */
  private mergeExtractionResults(
    skuExtracted: Partial<ExtractedProductData>,
    platformExtracted: Partial<ExtractedProductData>,
    existingData?: Partial<ExtractedProductData>
  ): ExtractedProductData {
    const hasSkuData = this.hasSignificantData(skuExtracted);
    const hasPlatformData = this.hasSignificantData(platformExtracted);
    
    let extractionSource: 'sku' | 'platform' | 'mixed';
    if (hasSkuData && hasPlatformData) {
      extractionSource = 'mixed';
    } else if (hasSkuData) {
      extractionSource = 'sku';
    } else {
      extractionSource = 'platform';
    }

    return {
      // SKU extraction takes priority, platform fills gaps
      style: skuExtracted.style || platformExtracted.style || existingData?.style,
      color: skuExtracted.color || platformExtracted.color || existingData?.color,
      size: skuExtracted.size || platformExtracted.size || existingData?.size,
      insideDesign: skuExtracted.insideDesign || platformExtracted.insideDesign || existingData?.insideDesign,
      outsideDesign: skuExtracted.outsideDesign || platformExtracted.outsideDesign || existingData?.outsideDesign,
      design: skuExtracted.design || platformExtracted.design || existingData?.design,
      inlayOption: skuExtracted.inlayOption || platformExtracted.inlayOption || existingData?.inlayOption,
      inlayType: skuExtracted.inlayType || platformExtracted.inlayType || existingData?.inlayType,
      isCustom: platformExtracted.isCustom || existingData?.isCustom || false,
      isEngrave: platformExtracted.isEngrave || existingData?.isEngrave || false,
      customization: platformExtracted.customization || existingData?.customization,
      engraving: platformExtracted.engraving || existingData?.engraving,
      extractionSource,
      confidence: 0, // Will be calculated separately
    };
  }

  /**
   * Calculate confidence score based on extraction results
   */
  private calculateConfidence(result: ExtractedProductData, input: ExtractionInput): number {
    let score = 0;
    let maxScore = 0;

    // Core attributes (higher weight)
    const coreAttributes = ['style', 'color', 'size'];
    coreAttributes.forEach(attr => {
      maxScore += 0.2;
      if (result[attr as keyof ExtractedProductData]) {
        score += 0.2;
      }
    });

    // Design attributes (medium weight)
    const designAttributes = ['design', 'insideDesign', 'outsideDesign'];
    designAttributes.forEach(attr => {
      maxScore += 0.1;
      if (result[attr as keyof ExtractedProductData]) {
        score += 0.1;
      }
    });

    // Additional attributes (lower weight)
    const additionalAttributes = ['inlayOption', 'inlayType'];
    additionalAttributes.forEach(attr => {
      maxScore += 0.05;
      if (result[attr as keyof ExtractedProductData]) {
        score += 0.05;
      }
    });

    // Bonus for having SKU data
    if (result.extractionSource === 'sku' || result.extractionSource === 'mixed') {
      score += 0.1;
    }
    maxScore += 0.1;

    return Math.min(1, score / maxScore);
  }

  /**
   * Check if extraction result has significant data
   */
  private hasSignificantData(extracted: Partial<ExtractedProductData>): boolean {
    const significantFields = ['style', 'color', 'size', 'design', 'insideDesign', 'outsideDesign'];
    return significantFields.some(field => extracted[field as keyof ExtractedProductData]);
  }

  /**
   * Convert platform source to extraction service source format
   */
  private mapPlatformToSource(platform: PlatformSource): 'shopify' | 'etsy' | 'amazon' {
    switch (platform) {
      case PlatformSource.SHOPIFY:
        return 'shopify';
      case PlatformSource.ETSY:
        return 'etsy';
      case PlatformSource.AMAZON:
        return 'amazon';
      default:
        return 'shopify'; // Default fallback
    }
  }

  /**
   * Convert size string (S05) to number (5)
   */
  private convertSizeToNumber(size?: string): number | undefined {
    if (!size) return undefined;
    const match = size.match(/S?(\d+)/);
    return match ? parseInt(match[1], 10) : undefined;
  }

  /**
   * Convert size number (5) to string (S05)
   */
  private convertNumberToSize(size?: number): string | undefined {
    if (!size) return undefined;
    return `S${size.toString().padStart(2, '0')}`;
  }

  /**
   * Validate extracted data for completeness
   */
  validateExtractedData(data: ExtractedProductData): {
    isValid: boolean;
    missingFields: string[];
    warnings: string[];
  } {
    const missingFields: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.style) missingFields.push('style');
    if (!data.color) missingFields.push('color');
    if (!data.size) missingFields.push('size');

    // Warnings for low confidence
    if (data.confidence < 0.5) {
      warnings.push('Low extraction confidence');
    }

    if (data.extractionSource === 'platform' && !data.design) {
      warnings.push('No design extracted from SKU, relying on platform data');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      warnings,
    };
  }
}
