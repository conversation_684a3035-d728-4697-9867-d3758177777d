import { Injectable } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import {
  PlatformAttributeExtractor,
  ExtractedAttributes,
  PlatformExtractionData,
} from '../platform-attribute-extractor.abstract';

export interface ShopifyProperty {
  name: string;
  value: string;
}

export interface ShopifyPlatformData {
  platform: 'shopify';
  productId: number;
  variantId: number;
  vendor?: string;
  properties?: ShopifyProperty[];
  variantTitle?: string | null;
  variantOptions?: string[];
}

@Injectable()
export class ShopifyAttributeExtractor extends PlatformAttributeExtractor {
  protected readonly platform = PlatformSource.SHOPIFY;

  /**
   * Extract attributes from Shopify-specific data
   */
  extractPlatformAttributes(data: PlatformExtractionData): ExtractedAttributes {
    const { skuExtracted, platformData } = data;

    if (!platformData || platformData.platform !== 'shopify') {
      return skuExtracted;
    }

    const shopifyData = platformData as ShopifyPlatformData;

    // Extract from properties
    const propertiesExtracted = this.extractFromProperties(shopifyData.properties || []);

    // Extract customization info
    const customizationInfo = this.extractCustomizationInfo(shopifyData.properties || []);

    // Merge all extracted data
    const platformExtracted: Partial<ExtractedAttributes> = {
      ...propertiesExtracted,
      ...customizationInfo,
    };

    return this.mergeAttributes(skuExtracted, platformExtracted);
  }

  /**
   * Extract attributes from Shopify properties
   */
  protected extractFromProperties(properties: ShopifyProperty[]): Partial<ExtractedAttributes> {
    const extracted: Partial<ExtractedAttributes> = {};

    for (const property of properties) {
      const name = property.name.toLowerCase().trim();
      const value = property.value.trim();

      // Skip empty values
      if (!value || value.toLowerCase() === 'none') {
        continue;
      }

      // Extract size
      if (name.includes('size') && !name.includes('text') && !name.includes('color')) {
        const size = this.normalizeSize(value);
        if (size) {
          extracted.size = size;
        }
      }

      // Extract color
      if (name.includes('color') && !name.includes('text') && !name.includes('inside')) {
        const color = this.normalizeColor(value);
        if (color) {
          extracted.color = color;
        }
      }

      // Extract style from variant title or properties
      if (name.includes('style') || name.includes('type')) {
        const style = this.normalizeStyle(value);
        if (style) {
          extracted.style = style;
        }
      }

      // Extract inside design
      if (name.includes('inside design') || name.includes('inner design')) {
        const insideDesign = this.normalizeInsideDesign(value);
        if (insideDesign) {
          extracted.insideDesign = insideDesign;
        }
      }

      // Extract outside design
      if (name.includes('outside design') || name.includes('outer design')) {
        const outsideDesign = this.normalizeOutsideDesign(value);
        if (outsideDesign) {
          extracted.outsideDesign = outsideDesign;
        }
      }

      // Extract design/pattern (fallback)
      if (name.includes('design') || name.includes('pattern')) {
        const design = this.normalizeDesign(value);
        if (design && !extracted.insideDesign) {
          extracted.insideDesign = design; // Default to inside design
        }
      }

      // Extract inlay option
      if (name.includes('inlay') || name.includes('fill')) {
        const inlayOption = this.extractInlayOption(value);
        if (inlayOption) {
          extracted.inlayOption = inlayOption;
        }

        // Also extract legacy inlay type
        const inlayType = this.extractInlayType(value);
        if (inlayType) {
          extracted.inlayType = inlayType;
        }
      }
    }

    return extracted;
  }

  /**
   * Extract customization and engraving info from Shopify properties
   */
  protected extractCustomizationInfo(properties: ShopifyProperty[]): {
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    let isCustom = false;
    let isEngrave = false;
    let customization: string | undefined;
    let engraving: string | undefined;

    for (const property of properties) {
      const name = property.name.toLowerCase().trim();
      const value = property.value.trim();

      // Check for engraving text
      if (name.includes('engraving') && name.includes('text')) {
        if (value && value.toLowerCase() !== 'none' && value.toLowerCase() !== 'no') {
          isEngrave = true;
          engraving = value;
        }
      }

      // Check for color fill on inside
      if (name.includes('color') && name.includes('inside')) {
        if (value && value.toLowerCase() !== 'no color fill') {
          isCustom = true;
          customization = value;
        }
      }

      // Check for other customization properties
      if (name.includes('custom') || name.includes('personalized')) {
        if (value && value.toLowerCase() !== 'none') {
          isCustom = true;
          customization = value;
        }
      }

      // Check property names for customization indicators
      const nameKeywords = this.checkCustomizationKeywords(name);
      if (nameKeywords.isCustom) {
        isCustom = true;
      }
      if (nameKeywords.isEngrave) {
        isEngrave = true;
      }
    }

    return {
      isCustom,
      isEngrave,
      customization,
      engraving,
    };
  }

  /**
   * Extract inlay type from value
   */
  private extractInlayType(value: string): 'gold' | 'silver' | 'clear' | 'none' | undefined {
    const lowerValue = value.toLowerCase();

    if (lowerValue.includes('gold')) {
      return 'gold';
    }
    if (lowerValue.includes('silver')) {
      return 'silver';
    }
    if (lowerValue.includes('clear') || lowerValue.includes('transparent')) {
      return 'clear';
    }
    if (lowerValue.includes('no') || lowerValue.includes('none') || lowerValue.includes('empty')) {
      return 'none';
    }

    return undefined;
  }

  /**
   * Helper method to extract attributes from variant title
   */
  extractFromVariantTitle(variantTitle?: string | null): Partial<ExtractedAttributes> {
    if (!variantTitle) {
      return {};
    }

    const extracted: Partial<ExtractedAttributes> = {};
    const lowerTitle = variantTitle.toLowerCase();

    // Extract size from variant title (e.g., "Size 6", "6mm", "6 (6mm bandwidth)")
    const sizeMatch = lowerTitle.match(/size\s*(\d+)|(\d+)\s*mm|(\d+)\s*\(/);
    if (sizeMatch) {
      const size = parseInt(sizeMatch[1] || sizeMatch[2] || sizeMatch[3], 10);
      if (size) {
        extracted.size = size;
      }
    }

    // Extract color from variant title
    const colorKeywords = [
      'rose gold',
      'rosegold',
      'rgold',
      'sky blue',
      'skyblue',
      'champagne',
      'champ',
      'northern lights',
      'nlights',
      'nl',
      'enchanted',
      'ef',
      'dark silver',
      'dsilver',
      'light gold',
      'lgold',
      'light grey',
      'lgrey',
      'baby blue',
      'bblue',
      'black marble',
      'bmarble',
      'white marble',
      'wmarble',
      'pure white',
      'pwhite',
      'pure pink',
      'ppink',
      'baby pink',
      'bpink',
      'neon pink',
      'neonp',
      'neon lemon',
      'neonl',
      'neon orange',
      'neono',
      'neon swirl',
      'neons',
      'dark bronze',
      'dbronze',
      'metal teal',
      'metalteal',
      'metal earth green',
      'metearthgreen',
      'star purple',
      'starpurple',
      'star frost',
      'starfrost',
      'aqua blue',
      'aquablue',
      'blue sky',
      'bluesky',
    ];

    for (const keyword of colorKeywords) {
      if (lowerTitle.includes(keyword)) {
        // Convert keyword to proper color name
        extracted.color = this.convertColorKeyword(keyword);
        break;
      }
    }

    return extracted;
  }

  /**
   * Convert color keyword to proper color name
   */
  private convertColorKeyword(keyword: string): string {
    const colorMappings: Record<string, string> = {
      'rose gold': 'RoseGold',
      rosegold: 'RoseGold',
      rgold: 'RoseGold',
      'sky blue': 'SkyBlue',
      skyblue: 'SkyBlue',
      champagne: 'Champagne',
      champ: 'Champagne',
      'northern lights': 'NLights',
      nlights: 'NLights',
      nl: 'NLights',
      enchanted: 'Enchanted',
      ef: 'Enchanted',
      'dark silver': 'DSilver',
      dsilver: 'DSilver',
      'light gold': 'LGold',
      lgold: 'LGold',
      'light grey': 'LGrey',
      lgrey: 'LGrey',
      'baby blue': 'BabyBlue',
      bblue: 'BabyBlue',
      'black marble': 'BMarble',
      bmarble: 'BMarble',
      'white marble': 'WMarble',
      wmarble: 'WMarble',
      'pure white': 'PWhite',
      pwhite: 'PWhite',
      'pure pink': 'PPink',
      ppink: 'PPink',
      'baby pink': 'BPink',
      bpink: 'BPink',
      'neon pink': 'NeonPink',
      neonp: 'NeonPink',
      'neon lemon': 'NeonLemon',
      neonl: 'NeonLemon',
      'neon orange': 'NeonOrange',
      neono: 'NeonOrange',
      'neon swirl': 'NeonSwirl',
      neons: 'NeonSwirl',
      'dark bronze': 'DarkBronze',
      dbronze: 'DarkBronze',
      'metal teal': 'MetalTeal',
      metalteal: 'MetalTeal',
      'metal earth green': 'MetEarthGreen',
      metearthgreen: 'MetEarthGreen',
      'star purple': 'StarPurple',
      starpurple: 'StarPurple',
      'star frost': 'StarFrost',
      starfrost: 'StarFrost',
      'aqua blue': 'AquaBlue',
      aquablue: 'AquaBlue',
      'blue sky': 'BlueSky',
      bluesky: 'BlueSky',
    };

    return colorMappings[keyword] || keyword;
  }
}
